// src/app/api/assignments/route.ts
/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  getCurrentUserId,
  getCurrentUserRole,
} from "@/lib/api-utils";
import { assignmentCreateSchema, paginationSchema } from "@/lib/validations";
import type { AssignmentResponse, AssignmentBaseData } from "@/types/api";
import {
  AssignmentStatus,
  AssignmentType,
  Prisma,
  UserRole,
  AcademicLevel,
  Priority,
  Spacing,
} from "@prisma/client";
import { nanoid } from "nanoid";
import { pricingService } from "@/lib/pricing-service";

// List assignments with filtering options
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // Extract pagination and search parameters
    const url = new URL(req.url);
    const pageParam = url.searchParams.get("page") ?? "1";
    // ROWS PER PAGE:- Fixed parameter extraction to properly handle perPage from URL
    const limitParam =
      url.searchParams.get("perPage") ?? url.searchParams.get("limit") ?? "10";
    const search = url.searchParams.get("search") ?? "";
    const status = url.searchParams.get("status");
    const assignmentType = url.searchParams.get("type");
    const subject = url.searchParams.get("subject");

    // ROWS PER PAGE:- Added validation and proper parsing of pagination parameters
    let parsedPage: number;
    let parsedLimit: number;

    try {
      parsedPage = parseInt(pageParam, 10);
      parsedLimit = parseInt(limitParam, 10);

      // ROWS PER PAGE:- Validate page and limit values
      if (isNaN(parsedPage) || parsedPage < 1) {
        parsedPage = 1;
      }
      if (isNaN(parsedLimit) || parsedLimit < 1) {
        parsedLimit = 10;
      }

      // ROWS PER PAGE:- Set reasonable limits to prevent abuse
      if (parsedLimit > 100) {
        parsedLimit = 100;
      }
    } catch (error) {
      // ROWS PER PAGE:- Fallback to defaults if parsing fails
      console.error("Error parsing pagination parameters:", error);
      parsedPage = 1;
      parsedLimit = 10;
    }

    const { page, limit } = paginationSchema.parse({
      page: parsedPage,
      limit: parsedLimit,
    });

    // ROWS PER PAGE:- Calculate skip using the properly parsed limit value
    const skip = (page - 1) * limit;

    // Build where conditions based on role and filters
    const whereConditions: Prisma.AssignmentWhereInput = {};

    // Add search condition if provided
    if (search) {
      whereConditions.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { subject: { contains: search, mode: "insensitive" } },
      ];
    }

    // Add status filter if provided
    if (
      status &&
      Object.values(AssignmentStatus).includes(status as AssignmentStatus)
    ) {
      whereConditions.status = status as AssignmentStatus;
    }

    // Add assignmentType filter if provided
    if (
      assignmentType &&
      Object.values(AssignmentType).includes(assignmentType as AssignmentType)
    ) {
      whereConditions.assignmentType = assignmentType as AssignmentType;
    }

    // Add subject filter if provided
    if (subject) {
      whereConditions.subject = subject;
    }

    // Role-based filtering
    switch (userRole) {
      case UserRole.ADMIN:
        // Admins can see all assignments
        break;
      case UserRole.CLIENT:
        // Clients can only see their own assignments
        whereConditions.clientId = currentUserId;
        break;
      case UserRole.WRITER:
        // Writers can see all assignments that are open for bidding or assigned to them
        whereConditions.OR = [
          ...(whereConditions.OR || []),
          { status: AssignmentStatus.POSTED },
          { assignedWriterId: currentUserId },
        ];
        break;
      default:
        return apiError("Invalid user role", 403);
    }

    // ROWS PER PAGE:- Get total count FIRST before applying pagination
    const totalCount = await prisma.assignment.count({
      where: whereConditions,
    });

    // ROWS PER PAGE:- Get assignments with the correct limit value
    const assignments = await prisma.assignment.findMany({
      where: whereConditions,
      skip,
      take: limit, // This now uses the properly parsed limit value
      orderBy: { createdAt: "desc" },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true, // Add this field
          },
        },
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true,
            rating: true,
            competencies: true,
          },
        },
        _count: {
          select: { bids: true },
        },
      },
    });

    // Format the response
    const formattedAssignments = assignments.map((assignment) => ({
      id: assignment.id,
      taskId: assignment.taskId,
      title: assignment.title,
      description: assignment.description,
      assignmentType: assignment.assignmentType,
      subject: assignment.subject,
      service: assignment.service,
      pageCount: assignment.pageCount,
      priority: assignment.priority,
      academicLevel: assignment.academicLevel,
      spacing: assignment.spacing,
      languageStyle: assignment.languageStyle,
      formatStyle: assignment.formatStyle,
      numSources: assignment.numSources,
      guidelines: assignment.guidelines,
      estTime: assignment.estTime.toISOString(),
      clientId: assignment.clientId,
      assignedWriterId: assignment.assignedWriterId,
      status: assignment.status,
      paymentStatus: assignment.paymentStatus,
      paypalOrderId: assignment.paypalOrderId,
      paypalPayerId: assignment.paypalPayerId,
      paypalPaymentId: assignment.paypalPaymentId,
      price: assignment.price,
      createdAt: assignment.createdAt.toISOString(),
      updatedAt: assignment.updatedAt.toISOString(),
      client: userRole === UserRole.ADMIN ? assignment.client : undefined,
      assignedWriter: assignment.assignedWriter ?? undefined,
      bidCount: assignment._count.bids,
    }));

    // ROWS PER PAGE:- Return the data with correct pagination info
    return apiSuccess({
      assignments: formattedAssignments,
      totalCount: totalCount,
      pagination: {
        total: totalCount,
        page,
        limit, // This now reflects the actual limit used
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching assignments:", error);
    return apiError("Failed to fetch assignments", 500);
  }
}

// Create a new assignment
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check user role - clients and admins can create assignments
    const permissionError = await checkPermission(["ADMIN", "CLIENT"]);
    if (permissionError) return permissionError;

    // Get current user ID and role
    const currentUserId = await getCurrentUserId();
    const userRole = await getCurrentUserRole();

    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    // Parse and validate the request body
    const parsed = await parseRequestBody(req, assignmentCreateSchema);
    if ("success" in parsed && parsed.success === false) {
      return apiError(parsed.message, 400, parsed.errors);
    }

    const assignmentData = parsed as AssignmentBaseData;

    // Calculate price using the unified pricing service
    const priceBreakdown = await pricingService.calculatePrice({
      academicLevel:
        assignmentData.academicLevel || AcademicLevel.UNDERGRADUATE,
      priority: assignmentData.priority || Priority.MEDIUM,
      spacing: assignmentData.spacing || Spacing.DOUBLE,
      pageCount: assignmentData.pageCount,
    });

    let finalPrice = priceBreakdown.finalPrice;
    let couponUsageId: string | undefined;

    // Handle coupon data if provided (coupon should already be applied from frontend)
    if (assignmentData.couponCode && currentUserId) {
      console.log("🔍 [ASSIGNMENT] Coupon data provided:", {
        code: assignmentData.couponCode,
        originalPrice: assignmentData.originalPrice,
        discountAmount: assignmentData.discountAmount,
      });

      // If coupon data is provided, use the pre-calculated prices
      if (assignmentData.originalPrice && assignmentData.discountAmount) {
        const expectedFinalPrice =
          assignmentData.originalPrice - assignmentData.discountAmount;

        // Validate that the provided coupon data is consistent
        if (Math.abs(expectedFinalPrice - finalPrice) > 0.01) {
          console.log("⚠️ [ASSIGNMENT] Price mismatch detected:", {
            calculatedPrice: finalPrice,
            expectedFinalPrice,
            originalPrice: assignmentData.originalPrice,
            discountAmount: assignmentData.discountAmount,
          });
        }

        finalPrice = expectedFinalPrice;

        // Find the existing coupon usage record
        const existingUsage = await prisma.couponUsage.findFirst({
          where: {
            userId: currentUserId,
            coupon: {
              code: assignmentData.couponCode,
            },
          },
          orderBy: {
            usedAt: "desc",
          },
        });

        if (existingUsage) {
          couponUsageId = existingUsage.id;
          console.log(
            "✅ [ASSIGNMENT] Found existing coupon usage:",
            couponUsageId
          );
        } else {
          console.log(
            "⚠️ [ASSIGNMENT] No existing coupon usage found, coupon may not have been properly applied"
          );
        }
      } else {
        console.log(
          "⚠️ [ASSIGNMENT] Coupon code provided but missing price data, skipping coupon application"
        );
      }
    }

    // If the user is a client, enforce they can only create assignments for themselves
    if (
      userRole === UserRole.CLIENT &&
      assignmentData.clientId !== currentUserId
    ) {
      return apiError("You can only create assignments for yourself", 403);
    }

    // Generate a unique taskId if not provided
    if (!assignmentData.taskId) {
      assignmentData.taskId = `TASK-${nanoid(8).toUpperCase()}`;
    }

    // Extract coupon-related fields that shouldn't be saved to Assignment model
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const {
      couponCode,
      originalPrice,
      discountAmount,
      ...assignmentDataForDB
    } = assignmentData;
    /* eslint-enable @typescript-eslint/no-unused-vars */

    // Create the assignment with final price (including coupon discount)
    const newAssignmentRaw = await prisma.assignment.create({
      data: {
        ...assignmentDataForDB,
        price: finalPrice,
        estTime: new Date(assignmentDataForDB.estTime),
        // Explicitly set status to POSTED
        // status: AssignmentStatus.POSTED,
        ...(assignmentDataForDB.status && {
          status: assignmentDataForDB.status,
        }),
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true, // Add this field
          },
        },
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true,
            accountId: true,
            rating: true,
            competencies: true,
          },
        },
      },
    });

    // Update coupon usage with assignment ID if coupon was applied
    if (couponUsageId && newAssignmentRaw.id) {
      try {
        await prisma.couponUsage.update({
          where: { id: couponUsageId },
          data: { assignmentId: newAssignmentRaw.id },
        });
      } catch (error) {
        console.error("Failed to link coupon usage to assignment:", error);
        // Don't fail the assignment creation if this fails
      }
    }

    // Format the response
    const newAssignment: AssignmentResponse = {
      ...newAssignmentRaw,
      estTime: newAssignmentRaw.estTime.toISOString(),
      createdAt: newAssignmentRaw.createdAt.toISOString(),
      updatedAt: newAssignmentRaw.updatedAt.toISOString(),
      client: userRole === UserRole.ADMIN ? newAssignmentRaw.client : undefined,
      assignedWriter: newAssignmentRaw.assignedWriter ?? undefined,
    };

    return apiSuccess(newAssignment, "Assignment created successfully");
  } catch (error) {
    console.error("Error creating assignment:", error);

    // Check for duplicate taskId
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return apiError("An assignment with this Task ID already exists", 409);
    }

    return apiError("Failed to create assignment", 500);
  }
}
