import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(req: NextRequest): Promise<NextResponse> {
  const { searchParams } = new URL(req.url);
  const token = searchParams.get("token");

  // Get base URL from environment or request
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${req.nextUrl.protocol}//${req.nextUrl.host}`;

  if (!token) {
    return NextResponse.redirect(new URL("/login?error=MissingToken", baseUrl));
  }

  const record = await prisma.emailVerificationToken.findUnique({ where: { token } });
  if (!record || record.expires < new Date()) {
    return NextResponse.redirect(new URL("/login?error=InvalidOrExpiredToken", baseUrl));
  }

  // Mark user as verified
  await prisma.user.update({
    where: { id: record.userId },
    data: { emailVerified: true },
  });

  // Delete token
  await prisma.emailVerificationToken.delete({ where: { token } });

  // Fetch user to determine role and redirect appropriately
  const user = await prisma.user.findUnique({ where: { id: record.userId } });
  let redirectPath = "/login?success=EmailVerified";

  if (user?.role === "WRITER") {
    redirectPath = "/writer/assessments";
  } else if (user?.role === "CLIENT") {
    redirectPath = "/client/dashboard";
  } else if (user?.role === "ADMIN") {
    redirectPath = "/admin/dashboard";
  }

  // Use absolute URL for redirect
  return NextResponse.redirect(new URL(redirectPath, baseUrl));
}
