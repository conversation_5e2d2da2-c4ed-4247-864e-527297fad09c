import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(req: NextRequest): Promise<NextResponse> {
  const { searchParams, origin } = new URL(req.url);
  const token = searchParams.get("token");

  if (!token) {
    return NextResponse.redirect(new URL("/login?error=MissingToken", origin));
  }

  const record = await prisma.emailVerificationToken.findUnique({ where: { token } });
  if (!record || record.expires < new Date()) {
    return NextResponse.redirect(new URL("/login?error=InvalidOrExpiredToken", origin));
  }

  // Mark user as verified
  await prisma.user.update({
    where: { id: record.userId },
    data: { emailVerified: true },
  });

  // Delete token
  await prisma.emailVerificationToken.delete({ where: { token } });

  // Fetch user to determine role
  const user = await prisma.user.findUnique({ where: { id: record.userId } });
  let redirectPath = "/login?success=EmailVerified";

  if (user?.role === "WRITER") {
    redirectPath = "/writer/assessments";
  } else if (user?.role === "CLIENT") {
    redirectPath = "/client/dashboard";
  } else if (user?.role === "ADMIN") {
    redirectPath = "/admin/dashboard";
  }

  return NextResponse.redirect(new URL(redirectPath, origin));
}
